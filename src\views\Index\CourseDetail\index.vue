<template>
    <div class="course-detail-page">
        <van-nav-bar
            title="课程详情"
            left-text="返回"
            left-arrow
            @click-left="goBack"
            fixed
            placeholder
        />

        <div class="content" v-if="courseDetail">
            <!-- 课程封面 -->
            <div class="course-header">
                <div class="course-cover">
                    <van-image :src="courseDetail.CoverImage" fit="cover" />

                    <!-- <div class="play-button" @click="previewCourse">
                        <van-icon name="play" />
                    </div> -->
                </div>
            </div>

            <!-- 课程基本信息 -->
            <div class="course-info">
                <h1 class="course-title">
                    {{ courseDetail.CourseName }}
                    <span class="course-tags">
                        <van-tag v-if="courseDetail.IsHot" type="warning" size="mini">热门</van-tag>
                        <van-tag v-if="courseDetail.Level === 1" type="primary" size="mini"
                            >初级</van-tag
                        >
                    </span>
                </h1>

                <div class="course-meta">
                    <div class="meta-item">
                        <van-icon name="manager-o" />
                        <span>{{ courseDetail.DocentName }}</span>
                    </div>
                    <div class="meta-item">
                        <van-icon name="clock-o" />
                        <span>{{ formatDuration(courseDetail.TotalDuration) }}</span>
                    </div>
                    <div class="meta-item">
                        <van-icon name="play-circle-o" />
                        <span>{{ courseDetail.TotalLessons }}个课时</span>
                    </div>
                </div>
                <div class="course-price">
                    <span class="current-price">免费</span>
                </div>
            </div>

            <!-- 课程描述 -->
            <div class="course-description">
                <h3>课程介绍</h3>
                <div class="description-content">
                    <p>{{ courseDetail.CourseDesc }}</p>
                </div>
            </div>

            <!-- 课程大纲 -->
            <div class="course-outline">
                <h3>课程大纲</h3>
                <div class="outline-list">
                    <div
                        v-for="(lesson, index) in courseDetail.Lessons"
                        :key="lesson.LessonID"
                        class="chapter-item"
                    >
                        <div class="chapter-header" @click="toggleLesson(index)">
                            <div class="chapter-info">
                                <div class="chapter-number">第{{ index + 1 }}章</div>
                                <span class="chapter-title">{{ lesson.LessonName }}</span>
                            </div>
                            <div class="chapter-meta">
                                <span class="lesson-count">{{
                                    formatDuration(lesson.Duration)
                                }}</span>
                                <van-icon
                                    :name="
                                        expandedLessons.includes(index) ? 'arrow-up' : 'arrow-down'
                                    "
                                />
                            </div>
                        </div>
                        <div v-show="expandedLessons.includes(index)" class="lessons-list">
                            <div
                                v-if="lesson.Resource"
                                class="lesson-item"
                                @click.stop="previewLesson(lesson)"
                            >
                                <van-icon :name="getResourceIcon(lesson.Resource.ResourceType)" />
                                <span class="lesson-title">{{ lesson.Resource.ResourceName }}</span>
                                <span class="lesson-duration">{{
                                    formatDuration(lesson.Resource.Duration || lesson.Duration)
                                }}</span>
                            </div>
                            <div v-else class="no-resource">暂无资源</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 讲师信息 -->
            <div class="teacher-info">
                <h3>讲师介绍</h3>
                <div class="teacher-card">
                    <van-image
                        round
                        width="60"
                        height="60"
                        src="https://img.yzcdn.cn/vant/cat.jpeg"
                    />
                    <div class="teacher-details">
                        <div class="teacher-name">{{ courseDetail.DocentName }}</div>
                        <div class="teacher-title">专业讲师</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="bottom-actions">
            <div class="action-buttons">
                <van-button
                    class="collect-btn"
                    :icon="isCollected ? 'star' : 'star-o'"
                    @click="toggleCollect"
                >
                    {{ isCollected ? '已收藏' : '收藏' }}
                </van-button>
                <van-button
                    type="primary"
                    class="enroll-btn"
                    :loading="enrollLoading"
                    @click="handleEnroll"
                    :disabled="isEnrolled"
                >
                    {{ getEnrollButtonText() }}
                </van-button>
            </div>
        </div>

        <!-- 报名确认弹窗 -->
        <van-dialog
            v-model="showEnrollDialog"
            title="确认报名"
            show-cancel-button
            @confirm="confirmEnroll"
            class="enroll-dialog"
        >
            <div class="enroll-info">
                <p class="notice">报名后需要管理员审核，请耐心等待</p>
            </div>
        </van-dialog>

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-container" />
    </div>
</template>

<script>
import { enrollCourse, getCourseDetail, getEnrollCourse } from '@/api/course'
export default {
    name: 'CourseDetailPage',
    data() {
        return {
            CourseID: this.$route.params.id,
            loading: false,
            enrollLoading: false,
            showEnrollDialog: false,
            isCollected: false,
            isEnrolled: false,
            courseDetail: null,
            expandedLessons: [] // 用于跟踪展开的课时
        }
    },
    created() {
        this.loadCourseDetail()
        this.checkEnrollStatus()
    },
    methods: {
        // 加载课程详情
        async loadCourseDetail() {
            try {
                this.loading = true
                const res = await getCourseDetail(this.$route.params.id)
                this.courseDetail = res.Data
            } catch (error) {
                this.$toast.fail('加载课程详情失败')
            } finally {
                this.loading = false
            }
        },

        // 返回上一页
        goBack() {
            this.$router.go(-1)
        },

        // 预览课程
        previewCourse() {
            this.$toast('课程预览功能开发中')
        },

        // 预览课时
        previewLesson(lesson) {
            if (lesson.Resource && lesson.Resource.ResourceUrl) {
                // 在实际项目中，这里应该跳转到播放页面
                window.open(lesson.Resource.ResourceUrl, '_blank')
            } else {
                this.$toast('该课时暂无资源')
            }
        },

        // 切换课时展开状态
        toggleLesson(index) {
            const indexOf = this.expandedLessons.indexOf(index)
            if (indexOf === -1) {
                this.expandedLessons.push(index)
            } else {
                this.expandedLessons.splice(indexOf, 1)
            }
        },

        // 获取资源图标
        getResourceIcon(resourceType) {
            const iconMap = {
                1: 'video-o', // 视频资源
                2: 'notes-o', // 文档资源
                3: 'audio-o' // 音频资源
            }
            return iconMap[resourceType] || 'play-circle-o'
        },

        // 格式化时长
        formatDuration(seconds) {
            if (!seconds) return '0分钟'
            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)

            if (hours > 0) {
                return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
            }
            return `${minutes}分钟`
        },

        // 切换收藏状态
        async toggleCollect() {
            try {
                this.isCollected = !this.isCollected
                this.$toast.success(this.isCollected ? '收藏成功' : '取消收藏')
            } catch (error) {
                this.$toast.fail('操作失败，请重试')
                this.isCollected = !this.isCollected // 恢复状态
            }
        },

        // 处理报名
        handleEnroll() {
            if (this.isEnrolled) {
                this.$toast('您已报名该课程')
                return
            }

            this.showEnrollDialog = true
        },
        // 查询报名
        async checkEnrollStatus() {
            getEnrollCourse({
                CourseId: this.CourseID,
                StudentId: this.$store.state.user.userInfo.UserID
            }).then(res => {
                console.log('已报名课程', res.Data.PageList)
                res.Data.PageList.some(item => {
                    if (item.CourseID === this.CourseID) {
                        this.isEnrolled = true
                        return true
                    }
                })
            })
        },
        // 确认报名
        async confirmEnroll() {
            try {
                this.enrollLoading = true
                const res = await enrollCourse({
                    courseId: this.CourseID,
                    studentld: this.$store.state.user.userInfo.UserID
                })
                console.log('报名', res)
                this.isEnrolled = true
                return

                // // 模拟API调用
                // await new Promise(resolve => setTimeout(resolve, 1500))

                // // 更新状态
                // this.isEnrolled = true
                // this.showEnrollDialog = false

                // this.$toast.success('报名成功！请等待管理员审核')

                // // 询问是否跳转到我的报名页面
                // setTimeout(() => {
                //     this.$dialog
                //         .confirm({
                //             title: '报名成功',
                //             message: '是否查看我的报名记录？'
                //         })
                //         .then(() => {
                //             this.$router.push('/my-enrollments')
                //         })
                //         .catch(() => {
                //             // 用户选择不跳转
                //         })
                // }, 1000)
            } catch (error) {
                this.$toast(error.response.data.Data)
            } finally {
                this.enrollLoading = false
            }
        },

        // 获取报名按钮文本
        getEnrollButtonText() {
            if (this.isEnrolled) {
                return '已报名待审核'
            }
            if (this.enrollLoading) {
                return '报名中...'
            }
            return '免费报名'
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.course-detail-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    ::v-deep .van-nav-bar__text {
        color: $white;
    }

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 0;

        // 课程封面
        .course-header {
            position: relative;

            .course-cover {
                position: relative;
                height: 220px;

                ::v-deep .van-image {
                    width: 100%;
                    height: 100%;
                }

                .course-tags {
                    position: absolute;
                    top: 16px;
                    left: 16px;
                    display: flex;
                    gap: 8px;
                }

                .play-button {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 60px;
                    height: 60px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:active {
                        transform: translate(-50%, -50%) scale(0.95);
                    }

                    .van-icon {
                        font-size: 24px;
                        color: $primary-blue;
                        margin-left: 2px;
                    }
                }
            }
        }

        // 课程基本信息
        .course-info {
            padding: 20px;
            background: $white;

            .course-title {
                font-size: 22px;
                font-weight: 700;
                color: $text-primary;
                margin: 0 0 16px 0;
                line-height: 1.4;
            }

            .course-meta {
                display: flex;
                gap: 20px;
                margin-bottom: 16px;

                .meta-item {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-size: 14px;
                    color: $text-secondary;

                    .van-icon {
                        color: $primary-blue;
                        font-size: 16px;
                    }
                }
            }

            .course-price {
                display: flex;
                align-items: baseline;
                gap: 12px;

                .current-price {
                    font-size: 24px;
                    font-weight: 700;
                    color: $primary-blue;
                }

                .original-price {
                    font-size: 16px;
                    color: $text-light;
                    text-decoration: line-through;
                }
            }
        }

        // 通用区块样式
        .course-description,
        .course-outline,
        .teacher-info,
        .course-reviews {
            background: $white;
            margin-top: 12px;
            padding: 20px;

            h3 {
                font-size: 18px;
                font-weight: 700;
                color: $text-primary;
                margin: 0 0 16px 0;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    left: -8px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 18px;
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    border-radius: 2px;
                }
            }
        }

        // 课程描述
        .course-description {
            .description-content {
                p {
                    line-height: 1.6;
                    color: $text-secondary;
                    margin: 0;
                }
            }
        }

        // 课程大纲
        .course-outline {
            .outline-list {
                .chapter-item {
                    border: 1px solid rgba(37, 99, 235, 0.1);
                    border-radius: 12px;
                    margin-bottom: 12px;
                    overflow: hidden;

                    .chapter-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 16px;
                        background: rgba(37, 99, 235, 0.02);
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:active {
                            background: rgba(37, 99, 235, 0.05);
                        }

                        .chapter-info {
                            display: flex;
                            align-items: center;
                            gap: 12px;

                            .chapter-number {
                                height: 24px;
                                line-height: 24px;
                                background: $primary-blue;
                                color: white;
                                padding: 0px 8px;
                                border-radius: 6px;
                                font-size: 12px;
                                font-weight: 600;
                            }

                            .chapter-title {
                                flex: 1;
                                font-size: 16px;
                                font-weight: 600;
                                color: $text-primary;
                                white-space: wrap;
                            }
                        }

                        .chapter-meta {
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            .lesson-count {
                                white-space: nowrap;
                                font-size: 12px;
                                color: $text-secondary;
                            }

                            .van-icon {
                                color: $text-secondary;
                                font-size: 16px;
                            }
                        }
                    }

                    .lessons-list {
                        .lesson-item {
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            padding: 12px 16px;
                            border-top: 1px solid rgba(37, 99, 235, 0.05);
                            cursor: pointer;
                            transition: all 0.3s ease;

                            &:hover {
                                background: rgba(37, 99, 235, 0.02);
                            }

                            .van-icon {
                                color: $primary-blue;
                                font-size: 16px;
                            }

                            .lesson-title {
                                flex: 1;
                                font-size: 14px;
                                color: $text-primary;
                            }

                            .lesson-duration {
                                font-size: 12px;
                                color: $text-secondary;
                            }
                        }

                        .no-resource {
                            padding: 12px 16px;
                            border-top: 1px solid rgba(37, 99, 235, 0.05);
                            font-size: 14px;
                            color: $text-light;
                            font-style: italic;
                        }
                    }
                }
            }
        }

        // 讲师信息
        .teacher-info {
            .teacher-card {
                display: flex;
                gap: 16px;
                margin-bottom: 16px;

                .teacher-details {
                    flex: 1;

                    .teacher-name {
                        font-size: 18px;
                        font-weight: 700;
                        color: $text-primary;
                        margin-bottom: 4px;
                    }

                    .teacher-title {
                        font-size: 14px;
                        color: $primary-blue;
                        font-weight: 600;
                        margin-bottom: 4px;
                    }

                    .teacher-experience {
                        font-size: 12px;
                        color: $text-secondary;
                    }
                }
            }

            .teacher-description {
                p {
                    line-height: 1.6;
                    color: $text-secondary;
                    margin: 0;
                }
            }
        }

        // 学员评价
        .course-reviews {
            .reviews-summary {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 20px;
                padding: 16px;
                background: rgba(37, 99, 235, 0.02);
                border-radius: 12px;

                .rating-score {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .score {
                        font-size: 24px;
                        font-weight: 700;
                        color: $primary-blue;
                    }
                }

                .rating-count {
                    font-size: 14px;
                    color: $text-secondary;
                }
            }

            .reviews-list {
                .review-item {
                    padding: 16px 0;
                    border-bottom: 1px solid rgba(37, 99, 235, 0.05);

                    &:last-child {
                        border-bottom: none;
                    }

                    .review-header {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        margin-bottom: 8px;

                        .review-user {
                            flex: 1;

                            .username {
                                font-size: 14px;
                                font-weight: 600;
                                color: $text-primary;
                                margin-bottom: 4px;
                            }
                        }

                        .review-time {
                            font-size: 12px;
                            color: $text-light;
                        }
                    }

                    .review-content {
                        font-size: 14px;
                        line-height: 1.6;
                        color: $text-secondary;
                    }
                }
            }
        }
    }

    // 底部操作栏
    .bottom-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: $white;
        padding: 12px 16px;
        box-shadow: 0 -4px 20px rgba(37, 99, 235, 0.1);
        z-index: 100;

        .action-buttons {
            width: 100%;
            display: flex;
            gap: 12px;

            .collect-btn {
                flex: 0 0 auto;
                padding: 0 16px;
                height: 44px;
                border: 1px solid rgba(37, 99, 235, 0.2);
                border-radius: 12px;
                background: $white;
                color: $primary-blue;
                font-weight: 600;

                &:active {
                    background: rgba(37, 99, 235, 0.05);
                }
            }

            .enroll-btn {
                flex: 1;
                height: 44px;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                border: none;
                box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);

                &:active {
                    transform: scale(0.98);
                }

                &:disabled {
                    background: $text-light;
                    box-shadow: none;
                }
            }
        }
    }

    // 加载状态
    .loading-container {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1000;
    }
}

// 报名弹窗样式
::v-deep .enroll-dialog {
    .van-dialog__content {
        padding: 24px;

        .enroll-info {
            p {
                margin: 8px 0;
                font-size: 14px;
                color: $text-primary;

                &.notice {
                    color: $text-secondary;
                    font-size: 12px;
                    background: rgba(37, 99, 235, 0.05);
                    padding: 8px 12px;
                    border-radius: 8px;
                    margin-top: 16px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .course-detail-page {
        .content {
            .course-info {
                padding: 16px;

                .course-title {
                    font-size: 20px;
                }

                .course-meta {
                    gap: 16px;
                    flex-wrap: wrap;
                }
            }

            .course-description,
            .course-outline,
            .teacher-info,
            .course-reviews {
                padding: 16px;

                h3 {
                    font-size: 16px;
                }
            }
        }

        .bottom-actions {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;

            .action-buttons {
                gap: 8px;

                .collect-btn {
                    padding: 0 12px;
                    height: 40px;
                }

                .enroll-btn {
                    height: 40px;
                    font-size: 15px;
                }
            }
        }
    }
}
</style>
